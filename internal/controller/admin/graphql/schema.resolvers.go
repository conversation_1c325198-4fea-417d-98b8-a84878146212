package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.76

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

// CreateTask is the resolver for the createTask field.
func (r *mutationResolver) CreateTask(ctx context.Context, input gql_model.CreateTaskInput) (*gql_model.ActivityTask, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.CreateTask(ctx, input)
}

// UpdateTask is the resolver for the updateTask field.
func (r *mutationResolver) UpdateTask(ctx context.Context, input gql_model.UpdateTaskInput) (*gql_model.ActivityTask, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.UpdateTask(ctx, input)
}

// DeleteTask is the resolver for the deleteTask field.
func (r *mutationResolver) DeleteTask(ctx context.Context, taskID string) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.DeleteTask(ctx, taskID)
}

// CreateTaskCategory is the resolver for the createTaskCategory field.
func (r *mutationResolver) CreateTaskCategory(ctx context.Context, input gql_model.CreateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.CreateTaskCategory(ctx, input)
}

// UpdateTaskCategory is the resolver for the updateTaskCategory field.
func (r *mutationResolver) UpdateTaskCategory(ctx context.Context, input gql_model.UpdateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.UpdateTaskCategory(ctx, input)
}

// DeleteTaskCategory is the resolver for the deleteTaskCategory field.
func (r *mutationResolver) DeleteTaskCategory(ctx context.Context, categoryID string) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.DeleteTaskCategory(ctx, categoryID)
}

// CreateTierBenefit is the resolver for the createTierBenefit field.
func (r *mutationResolver) CreateTierBenefit(ctx context.Context, input gql_model.CreateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.CreateTierBenefit(ctx, input)
}

// UpdateTierBenefit is the resolver for the updateTierBenefit field.
func (r *mutationResolver) UpdateTierBenefit(ctx context.Context, input gql_model.UpdateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.UpdateTierBenefit(ctx, input)
}

// DeleteTierBenefit is the resolver for the deleteTierBenefit field.
func (r *mutationResolver) DeleteTierBenefit(ctx context.Context, tierBenefitID string) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.DeleteTierBenefit(ctx, tierBenefitID)
}

// AdminResetDailyTasks is the resolver for the adminResetDailyTasks field.
func (r *mutationResolver) AdminResetDailyTasks(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminResetDailyTasks(ctx)
}

// AdminResetWeeklyTasks is the resolver for the adminResetWeeklyTasks field.
func (r *mutationResolver) AdminResetWeeklyTasks(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminResetWeeklyTasks(ctx)
}

// AdminResetMonthlyTasks is the resolver for the adminResetMonthlyTasks field.
func (r *mutationResolver) AdminResetMonthlyTasks(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminResetMonthlyTasks(ctx)
}

// AdminRecalculateAllUserTiers is the resolver for the adminRecalculateAllUserTiers field.
func (r *mutationResolver) AdminRecalculateAllUserTiers(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminRecalculateAllUserTiers(ctx)
}

// AdminSeedInitialTasks is the resolver for the adminSeedInitialTasks field.
func (r *mutationResolver) AdminSeedInitialTasks(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminSeedInitialTasks(ctx)
}

// AdminGetAllTasks is the resolver for the adminGetAllTasks field.
func (r *queryResolver) AdminGetAllTasks(ctx context.Context) ([]*gql_model.ActivityTask, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminGetAllTasks(ctx)
}

// AdminGetTaskCompletionStats is the resolver for the adminGetTaskCompletionStats field.
func (r *queryResolver) AdminGetTaskCompletionStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminTaskCompletionStatsResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminGetTaskCompletionStats(ctx, input)
}

// AdminGetUserActivityStats is the resolver for the adminGetUserActivityStats field.
func (r *queryResolver) AdminGetUserActivityStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminUserActivityStatsResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminGetUserActivityStats(ctx, input)
}

// AdminGetTierDistribution is the resolver for the adminGetTierDistribution field.
func (r *queryResolver) AdminGetTierDistribution(ctx context.Context) (*gql_model.AdminTierDistributionResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminGetTierDistribution(ctx)
}

// AdminGetTopUsers is the resolver for the adminGetTopUsers field.
func (r *queryResolver) AdminGetTopUsers(ctx context.Context, limit *int) ([]*gql_model.UserTierInfo, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminGetTopUsers(ctx, limit)
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
