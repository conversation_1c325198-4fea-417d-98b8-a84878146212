// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package graphql

import (
	"context"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
)

// NewExecutableSchema creates an ExecutableSchema from the ResolverRoot interface.
func NewExecutableSchema(cfg Config) graphql.ExecutableSchema {
	return &executableSchema{
		resolvers:  cfg.Resolvers,
		directives: cfg.Directives,
		complexity: cfg.Complexity,
	}
}

type Config struct {
	Resolvers  ResolverRoot
	Directives DirectiveRoot
	Complexity ComplexityRoot
}

type ResolverRoot interface {
	Mutation() MutationResolver
	Query() QueryResolver
}

type DirectiveRoot struct {
	AdminAuth func(ctx context.Context, obj interface{}, next graphql.Resolver) (res interface{}, err error)
}

type ComplexityRoot struct {
	Mutation struct {
		AdminRecalculateAllUserTiers func(childComplexity int) int
		AdminResetDailyTasks         func(childComplexity int) int
		AdminResetMonthlyTasks       func(childComplexity int) int
		AdminResetWeeklyTasks        func(childComplexity int) int
		AdminSeedInitialTasks        func(childComplexity int) int
		CreateTask                   func(childComplexity int, input gql_model.CreateTaskInput) int
		CreateTaskCategory           func(childComplexity int, input gql_model.CreateTaskCategoryInput) int
		CreateTierBenefit            func(childComplexity int, input gql_model.CreateTierBenefitInput) int
		DeleteTask                   func(childComplexity int, taskID string) int
		DeleteTaskCategory           func(childComplexity int, categoryID string) int
		DeleteTierBenefit            func(childComplexity int, tierBenefitID string) int
		UpdateTask                   func(childComplexity int, input gql_model.UpdateTaskInput) int
		UpdateTaskCategory           func(childComplexity int, input gql_model.UpdateTaskCategoryInput) int
		UpdateTierBenefit            func(childComplexity int, input gql_model.UpdateTierBenefitInput) int
	}

	Query struct {
		AdminGetAllTasks            func(childComplexity int) int
		AdminGetTaskCompletionStats func(childComplexity int, input gql_model.AdminStatsInput) int
		AdminGetTierDistribution    func(childComplexity int) int
		AdminGetTopUsers            func(childComplexity int, limit *int) int
		AdminGetUserActivityStats   func(childComplexity int, input gql_model.AdminStatsInput) int
	}
}

type MutationResolver interface {
	CreateTask(ctx context.Context, input gql_model.CreateTaskInput) (*gql_model.ActivityTask, error)
	UpdateTask(ctx context.Context, input gql_model.UpdateTaskInput) (*gql_model.ActivityTask, error)
	DeleteTask(ctx context.Context, taskID string) (bool, error)
	CreateTaskCategory(ctx context.Context, input gql_model.CreateTaskCategoryInput) (*gql_model.TaskCategory, error)
	UpdateTaskCategory(ctx context.Context, input gql_model.UpdateTaskCategoryInput) (*gql_model.TaskCategory, error)
	DeleteTaskCategory(ctx context.Context, categoryID string) (bool, error)
	CreateTierBenefit(ctx context.Context, input gql_model.CreateTierBenefitInput) (*gql_model.TierBenefitResponse, error)
	UpdateTierBenefit(ctx context.Context, input gql_model.UpdateTierBenefitInput) (*gql_model.TierBenefitResponse, error)
	DeleteTierBenefit(ctx context.Context, tierBenefitID string) (bool, error)
	AdminResetDailyTasks(ctx context.Context) (bool, error)
	AdminResetWeeklyTasks(ctx context.Context) (bool, error)
	AdminResetMonthlyTasks(ctx context.Context) (bool, error)
	AdminRecalculateAllUserTiers(ctx context.Context) (bool, error)
	AdminSeedInitialTasks(ctx context.Context) (bool, error)
}

type QueryResolver interface {
	AdminGetAllTasks(ctx context.Context) ([]*gql_model.ActivityTask, error)
	AdminGetTaskCompletionStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminTaskCompletionStatsResponse, error)
	AdminGetUserActivityStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminUserActivityStatsResponse, error)
	AdminGetTierDistribution(ctx context.Context) (*gql_model.AdminTierDistributionResponse, error)
	AdminGetTopUsers(ctx context.Context, limit *int) ([]*gql_model.UserTierInfo, error)
}

type executableSchema struct {
	resolvers  ResolverRoot
	directives DirectiveRoot
	complexity ComplexityRoot
}

func (e *executableSchema) Schema() *ast.SchemaDocument {
	return parsedSchema
}

func (e *executableSchema) Complexity(typeName, fieldName string, childComplexity int, rawArgs map[string]interface{}) (int, bool) {
	ec := executionContext{nil, e}
	_ = ec
	switch typeName + "." + fieldName {

	case "Mutation.adminRecalculateAllUserTiers":
		if e.complexity.Mutation.AdminRecalculateAllUserTiers == nil {
			break
		}

		return e.complexity.Mutation.AdminRecalculateAllUserTiers(childComplexity), true

	case "Mutation.adminResetDailyTasks":
		if e.complexity.Mutation.AdminResetDailyTasks == nil {
			break
		}

		return e.complexity.Mutation.AdminResetDailyTasks(childComplexity), true

	case "Mutation.adminResetMonthlyTasks":
		if e.complexity.Mutation.AdminResetMonthlyTasks == nil {
			break
		}

		return e.complexity.Mutation.AdminResetMonthlyTasks(childComplexity), true

	case "Mutation.adminResetWeeklyTasks":
		if e.complexity.Mutation.AdminResetWeeklyTasks == nil {
			break
		}

		return e.complexity.Mutation.AdminResetWeeklyTasks(childComplexity), true

	case "Mutation.adminSeedInitialTasks":
		if e.complexity.Mutation.AdminSeedInitialTasks == nil {
			break
		}

		return e.complexity.Mutation.AdminSeedInitialTasks(childComplexity), true

	case "Mutation.createTask":
		if e.complexity.Mutation.CreateTask == nil {
			break
		}

		args, err := ec.field_Mutation_createTask_args(context.TODO(), rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateTask(childComplexity, args["input"].(gql_model.CreateTaskInput)), true

	case "Mutation.createTaskCategory":
		if e.complexity.Mutation.CreateTaskCategory == nil {
			break
		}

		args, err := ec.field_Mutation_createTaskCategory_args(context.TODO(), rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateTaskCategory(childComplexity, args["input"].(gql_model.CreateTaskCategoryInput)), true

	case "Mutation.createTierBenefit":
		if e.complexity.Mutation.CreateTierBenefit == nil {
			break
		}

		args, err := ec.field_Mutation_createTierBenefit_args(context.TODO(), rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.CreateTierBenefit(childComplexity, args["input"].(gql_model.CreateTierBenefitInput)), true

	case "Mutation.deleteTask":
		if e.complexity.Mutation.DeleteTask == nil {
			break
		}

		args, err := ec.field_Mutation_deleteTask_args(context.TODO(), rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.DeleteTask(childComplexity, args["taskId"].(string)), true

	case "Mutation.deleteTaskCategory":
		if e.complexity.Mutation.DeleteTaskCategory == nil {
			break
		}

		args, err := ec.field_Mutation_deleteTaskCategory_args(context.TODO(), rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.DeleteTaskCategory(childComplexity, args["categoryId"].(string)), true

	case "Mutation.deleteTierBenefit":
		if e.complexity.Mutation.DeleteTierBenefit == nil {
			break
		}

		args, err := ec.field_Mutation_deleteTierBenefit_args(context.TODO(), rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.DeleteTierBenefit(childComplexity, args["tierBenefitId"].(string)), true

	case "Mutation.updateTask":
		if e.complexity.Mutation.UpdateTask == nil {
			break
		}

		args, err := ec.field_Mutation_updateTask_args(context.TODO(), rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateTask(childComplexity, args["input"].(gql_model.UpdateTaskInput)), true

	case "Mutation.updateTaskCategory":
		if e.complexity.Mutation.UpdateTaskCategory == nil {
			break
		}

		args, err := ec.field_Mutation_updateTaskCategory_args(context.TODO(), rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateTaskCategory(childComplexity, args["input"].(gql_model.UpdateTaskCategoryInput)), true

	case "Mutation.updateTierBenefit":
		if e.complexity.Mutation.UpdateTierBenefit == nil {
			break
		}

		args, err := ec.field_Mutation_updateTierBenefit_args(context.TODO(), rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Mutation.UpdateTierBenefit(childComplexity, args["input"].(gql_model.UpdateTierBenefitInput)), true

	case "Query.adminGetAllTasks":
		if e.complexity.Query.AdminGetAllTasks == nil {
			break
		}

		return e.complexity.Query.AdminGetAllTasks(childComplexity), true

	case "Query.adminGetTaskCompletionStats":
		if e.complexity.Query.AdminGetTaskCompletionStats == nil {
			break
		}

		args, err := ec.field_Query_adminGetTaskCompletionStats_args(context.TODO(), rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.AdminGetTaskCompletionStats(childComplexity, args["input"].(gql_model.AdminStatsInput)), true

	case "Query.adminGetTierDistribution":
		if e.complexity.Query.AdminGetTierDistribution == nil {
			break
		}

		return e.complexity.Query.AdminGetTierDistribution(childComplexity), true

	case "Query.adminGetTopUsers":
		if e.complexity.Query.AdminGetTopUsers == nil {
			break
		}

		args, err := ec.field_Query_adminGetTopUsers_args(context.TODO(), rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.AdminGetTopUsers(childComplexity, args["limit"].(*int)), true

	case "Query.adminGetUserActivityStats":
		if e.complexity.Query.AdminGetUserActivityStats == nil {
			break
		}

		args, err := ec.field_Query_adminGetUserActivityStats_args(context.TODO(), rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.AdminGetUserActivityStats(childComplexity, args["input"].(gql_model.AdminStatsInput)), true

	}
	return 0, false
}

type executionContext struct {
	*graphql.RequestContext
	*executableSchema
}

// Temporary placeholder functions - these will be generated by gqlgen
func (ec *executionContext) field_Mutation_createTask_args(ctx context.Context, rawArgs map[string]interface{}) (map[string]interface{}, error) {
	return rawArgs, nil
}

func (ec *executionContext) field_Mutation_createTaskCategory_args(ctx context.Context, rawArgs map[string]interface{}) (map[string]interface{}, error) {
	return rawArgs, nil
}

func (ec *executionContext) field_Mutation_createTierBenefit_args(ctx context.Context, rawArgs map[string]interface{}) (map[string]interface{}, error) {
	return rawArgs, nil
}

func (ec *executionContext) field_Mutation_deleteTask_args(ctx context.Context, rawArgs map[string]interface{}) (map[string]interface{}, error) {
	return rawArgs, nil
}

func (ec *executionContext) field_Mutation_deleteTaskCategory_args(ctx context.Context, rawArgs map[string]interface{}) (map[string]interface{}, error) {
	return rawArgs, nil
}

func (ec *executionContext) field_Mutation_deleteTierBenefit_args(ctx context.Context, rawArgs map[string]interface{}) (map[string]interface{}, error) {
	return rawArgs, nil
}

func (ec *executionContext) field_Mutation_updateTask_args(ctx context.Context, rawArgs map[string]interface{}) (map[string]interface{}, error) {
	return rawArgs, nil
}

func (ec *executionContext) field_Mutation_updateTaskCategory_args(ctx context.Context, rawArgs map[string]interface{}) (map[string]interface{}, error) {
	return rawArgs, nil
}

func (ec *executionContext) field_Mutation_updateTierBenefit_args(ctx context.Context, rawArgs map[string]interface{}) (map[string]interface{}, error) {
	return rawArgs, nil
}

func (ec *executionContext) field_Query_adminGetTaskCompletionStats_args(ctx context.Context, rawArgs map[string]interface{}) (map[string]interface{}, error) {
	return rawArgs, nil
}

func (ec *executionContext) field_Query_adminGetTopUsers_args(ctx context.Context, rawArgs map[string]interface{}) (map[string]interface{}, error) {
	return rawArgs, nil
}

func (ec *executionContext) field_Query_adminGetUserActivityStats_args(ctx context.Context, rawArgs map[string]interface{}) (map[string]interface{}, error) {
	return rawArgs, nil
}

// Placeholder for schema parsing - will be generated by gqlgen
var parsedSchema *ast.SchemaDocument
